<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Order;
use PDO;

/**
 * Storage Service
 * Manages storage monitoring, synchronization and health metrics
 */
final class StorageService
{
    private ?PDO $mysqlConnection = null;
    private string $dataDir;
    private string $jsonFile;

    public function __construct()
    {
        $this->dataDir = dirname(__DIR__, 2) . '/data';
        $this->jsonFile = $this->dataDir . '/orders.json';

        // Ensure data directory exists
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
    }

    public function getStatus(): array
    {
        $mysqlStatus = $this->getMySQLStatus();
        $jsonStatus = $this->getJSONStatus();

        return [
            'mysql' => $mysqlStatus,
            'json' => $jsonStatus,
            'last_sync' => $this->getLastSyncTime(),
            'overall_health' => $mysqlStatus['connected'] && $jsonStatus['connected'] ? 'healthy' : 'warning'
        ];
    }

    public function getSyncStats(): array
    {
        try {
            $mysqlCount = $this->getMySQLOrderCount();
            $jsonCount = $this->getJSONOrderCount();

            return [
                'mysql_records' => $mysqlCount,
                'json_records' => $jsonCount,
                'sync_needed' => $mysqlCount !== $jsonCount,
                'difference' => abs($mysqlCount - $jsonCount)
            ];
        } catch (\Exception $e) {
            return [
                'mysql_records' => 0,
                'json_records' => 0,
                'sync_needed' => false,
                'difference' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    public function sync(bool $forceReplace = false): array
    {
        try {
            $startTime = microtime(true);
            $syncedRecords = 0;
            $errors = [];

            // Get all orders from MySQL (primary source)
            $mysqlOrders = Order::getAll();

            if ($forceReplace) {
                // Clear JSON storage first
                $this->clearJSONStorage();
            }

            // Sync each order to JSON
            foreach ($mysqlOrders as $order) {
                try {
                    if ($forceReplace || !$this->jsonOrderExists($order['id'])) {
                        if ($this->saveOrderToJSON($order)) {
                            $syncedRecords++;
                        }
                    }
                } catch (\Exception $e) {
                    $errors[] = "Failed to sync order {$order['id']}: " . $e->getMessage();
                }
            }

            // Update last sync timestamp
            $this->updateLastSyncTime();

            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 3);

            return [
                'success' => true,
                'synced_records' => $syncedRecords,
                'total_records' => count($mysqlOrders),
                'duration_seconds' => $duration,
                'errors' => $errors,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'synced_records' => 0,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }

    public function getHealthMetrics(): array
    {
        $mysqlHealth = $this->getMySQLHealth();
        $jsonHealth = $this->getJSONHealth();
        $syncStats = $this->getSyncStats();

        return [
            'mysql' => $mysqlHealth,
            'json' => $jsonHealth,
            'sync' => [
                'status' => $syncStats['sync_needed'] ? 'out_of_sync' : 'synchronized',
                'last_sync' => $this->getLastSyncTime(),
                'difference' => $syncStats['difference']
            ],
            'overall_score' => $this->calculateHealthScore($mysqlHealth, $jsonHealth, $syncStats)
        ];
    }

    public function getLastSyncTime(): ?string
    {
        $syncFile = $this->dataDir . '/last_sync.txt';
        if (file_exists($syncFile)) {
            return trim(file_get_contents($syncFile));
        }
        return null;
    }

    private function getMySQLConnection(): PDO
    {
        if ($this->mysqlConnection === null) {
            $config = require dirname(__DIR__, 2) . '/config/database.php';
            $mysql = $config['mysql'];

            $dsn = "mysql:host={$mysql['host']};port={$mysql['port']};dbname={$mysql['database']};charset=utf8mb4";

            $this->mysqlConnection = new PDO($dsn, $mysql['username'], $mysql['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        }

        return $this->mysqlConnection;
    }

    private function getMySQLStatus(): array
    {
        try {
            $pdo = $this->getMySQLConnection();
            $stmt = $pdo->query('SELECT COUNT(*) as count FROM orders');
            $count = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] ?? 0;

            return [
                'connected' => true,
                'records' => (int) $count,
                'type' => 'mysql',
                'status' => 'healthy'
            ];
        } catch (\Exception $e) {
            return [
                'connected' => false,
                'records' => 0,
                'type' => 'mysql',
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getJSONStatus(): array
    {
        try {
            $count = $this->getJSONOrderCount();

            return [
                'connected' => true,
                'records' => $count,
                'type' => 'json',
                'status' => 'healthy'
            ];
        } catch (\Exception $e) {
            return [
                'connected' => false,
                'records' => 0,
                'type' => 'json',
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getMySQLHealth(): array
    {
        try {
            $pdo = $this->getMySQLConnection();

            // Check connection
            $pdo->query('SELECT 1');

            // Get table status
            $stmt = $pdo->query('SHOW TABLE STATUS LIKE "orders"');
            $tableInfo = $stmt->fetch(\PDO::FETCH_ASSOC);

            return [
                'connection' => 'healthy',
                'table_size' => $tableInfo['Data_length'] ?? 0,
                'row_count' => $tableInfo['Rows'] ?? 0,
                'engine' => $tableInfo['Engine'] ?? 'unknown'
            ];
        } catch (\Exception $e) {
            return [
                'connection' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getJSONHealth(): array
    {
        try {
            $health = [
                'file_exists' => file_exists($this->jsonFile),
                'readable' => is_readable($this->jsonFile),
                'writable' => is_writable(dirname($this->jsonFile))
            ];

            if ($health['file_exists']) {
                $health['file_size'] = filesize($this->jsonFile);
                $health['last_modified'] = date('Y-m-d H:i:s', filemtime($this->jsonFile));
            }

            return $health;
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }

    private function calculateHealthScore(array $mysqlHealth, array $jsonHealth, array $syncStats): int
    {
        $score = 100;

        // Deduct points for MySQL issues
        if (isset($mysqlHealth['connection']) && $mysqlHealth['connection'] !== 'healthy') {
            $score -= 40;
        }

        // Deduct points for JSON issues
        if (!($jsonHealth['file_exists'] ?? false)) {
            $score -= 20;
        }
        if (!($jsonHealth['writable'] ?? false)) {
            $score -= 10;
        }

        // Deduct points for sync issues
        if ($syncStats['sync_needed'] ?? false) {
            $score -= 15;
        }

        return max(0, $score);
    }

    private function updateLastSyncTime(): void
    {
        file_put_contents(
            $this->dataDir . '/last_sync.txt',
            date('Y-m-d H:i:s')
        );
    }

    private function getMySQLOrderCount(): int
    {
        try {
            $pdo = $this->getMySQLConnection();
            $stmt = $pdo->query('SELECT COUNT(*) as count FROM orders');
            return (int) $stmt->fetch(\PDO::FETCH_ASSOC)['count'];
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getJSONOrderCount(): int
    {
        try {
            if (!file_exists($this->jsonFile)) {
                return 0;
            }

            $content = file_get_contents($this->jsonFile);
            if (empty($content)) {
                return 0;
            }

            $data = json_decode($content, true);
            return count($data ?? []);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function clearJSONStorage(): void
    {
        file_put_contents($this->jsonFile, '[]');
    }

    private function jsonOrderExists(int $orderId): bool
    {
        try {
            if (!file_exists($this->jsonFile)) {
                return false;
            }

            $content = file_get_contents($this->jsonFile);
            if (empty($content)) {
                return false;
            }

            $orders = json_decode($content, true);
            if (!is_array($orders)) {
                return false;
            }

            foreach ($orders as $order) {
                if (isset($order['id']) && $order['id'] == $orderId) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    private function saveOrderToJSON(array $order): bool
    {
        try {
            $orders = [];

            if (file_exists($this->jsonFile)) {
                $content = file_get_contents($this->jsonFile);
                if (!empty($content)) {
                    $orders = json_decode($content, true) ?? [];
                }
            }

            // Remove existing order with same ID
            $orders = array_filter($orders, fn($o) => $o['id'] != $order['id']);

            // Add new order
            $orders[] = $order;

            return file_put_contents($this->jsonFile, json_encode($orders, JSON_PRETTY_PRINT)) !== false;
        } catch (\Exception $e) {
            return false;
        }
    }
}
