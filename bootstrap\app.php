<?php

declare(strict_types=1);

/**
 * Laravel-style Application Bootstrap
 * DrxDion E-commerce Platform
 */

// Debug: Log bootstrap start
error_log("BOOTSTRAP: Starting application bootstrap");

// Define base path
define('BASE_PATH', dirname(__DIR__));
error_log("BOOTSTRAP: BASE_PATH defined as: " . BASE_PATH);

// Composer autoloader
if (file_exists(BASE_PATH . '/vendor/autoload.php')) {
    require_once BASE_PATH . '/vendor/autoload.php';
}

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $envContent = file_get_contents(BASE_PATH . '/.env');
    $envLines = explode("\n", $envContent);
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || str_starts_with($line, '#')) {
            continue;
        }
        if (str_contains($line, '=')) {
            [$key, $value] = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Simple service container
class Container
{
    private static array $services = [];

    public static function bind(string $abstract, callable $concrete): void
    {
        self::$services[$abstract] = $concrete;
    }

    public static function resolve(string $abstract): mixed
    {
        if (isset(self::$services[$abstract])) {
            return self::$services[$abstract]();
        }

        // Try to auto-resolve
        if (class_exists($abstract)) {
            return new $abstract();
        }

        throw new Exception("Service {$abstract} not found");
    }
}

// Register services
error_log("BOOTSTRAP: Registering services...");
Container::bind('App\Services\StorageService', function () {
    return new App\Services\StorageService();
});
error_log("BOOTSTRAP: StorageService registered");

Container::bind('App\Http\Controllers\AdminController', function () {
    error_log("BOOTSTRAP: Creating AdminController...");
    $storageService = Container::resolve('App\Services\StorageService');
    error_log("BOOTSTRAP: StorageService resolved for AdminController");
    return new App\Http\Controllers\AdminController($storageService);
});
error_log("BOOTSTRAP: AdminController registered");

Container::bind('App\Http\Controllers\ApiController', function () {
    error_log("BOOTSTRAP: Creating ApiController...");
    $adminController = Container::resolve('App\Http\Controllers\AdminController');
    error_log("BOOTSTRAP: AdminController resolved for ApiController");
    $storageService = Container::resolve('App\Services\StorageService');
    error_log("BOOTSTRAP: StorageService resolved for ApiController");
    return new App\Http\Controllers\ApiController($adminController, $storageService);
});
error_log("BOOTSTRAP: ApiController registered");

Container::bind('App\Http\Controllers\PageController', function () {
    error_log("BOOTSTRAP: Creating PageController...");
    return new App\Http\Controllers\PageController();
});
error_log("BOOTSTRAP: PageController registered");

// Start session for admin authentication
error_log("BOOTSTRAP: Starting session...");
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
error_log("BOOTSTRAP: Session started");

// Set timezone
error_log("BOOTSTRAP: Setting timezone...");
date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'UTC');
error_log("BOOTSTRAP: Timezone set");

// Error reporting - simplified
error_log("BOOTSTRAP: Setting error reporting...");
// Skip error reporting for now to avoid issues
error_log("BOOTSTRAP: Error reporting skipped");

error_log("BOOTSTRAP: Bootstrap completed successfully");
return Container::class;
