<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Services\StorageService;

/**
 * API Controller
 * Laravel-style API endpoints
 */
final class ApiController extends Controller
{
    public function __construct(
        private readonly AdminController $adminController,
        private readonly StorageService $storageService
    ) {}

    public function csrfToken(): void
    {
        // Get or generate CSRF token
        if (empty($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }

        $token = $_SESSION['csrf_token'];

        // Also set as cookie for JavaScript access
        setcookie('csrf-token', $token, [
            'path' => '/',
            'httponly' => false,
            'secure' => isset($_SERVER['HTTPS']),
            'samesite' => 'Strict'
        ]);

        $this->successResponse(['token' => $token]);
    }

    public function getSettings(): void
    {
        $result = $this->adminController->getSettings();

        if ($result['success']) {
            $this->successResponse($result['data'], 'Settings loaded successfully');
        } else {
            $this->errorResponse($result['error'], 500);
        }
    }

    public function updateSettings(): void
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (!$data) {
            $this->errorResponse('Invalid JSON data', 400);
            return;
        }

        $result = $this->adminController->updateSettings($data);

        if ($result['success']) {
            $this->successResponse([], $result['message']);
        } else {
            $this->errorResponse($result['error'], 400);
        }
    }

    public function getOrders(): void
    {
        try {
            $orders = \App\Models\Order::getAll();
            $this->successResponse($orders, 'Orders loaded successfully');
        } catch (\Exception $e) {
            $this->errorResponse('Failed to load orders: ' . $e->getMessage(), 500);
        }
    }

    public function getStorageStatus(): void
    {
        $result = $this->adminController->getStorageStatus();

        if ($result['success']) {
            $this->successResponse($result['data'], 'Storage status loaded successfully');
        } else {
            $this->errorResponse($result['error'], 500);
        }
    }

    public function syncStorage(): void
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true) ?? [];

        $result = $this->adminController->syncStorage($data);

        if ($result['success']) {
            $this->successResponse($result['data'], $result['message']);
        } else {
            $this->errorResponse($result['error'], 500);
        }
    }

    public function contact(): void
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (!$data || empty($data['name']) || empty($data['email']) || empty($data['message'])) {
            $this->errorResponse('All fields are required', 400);
            return;
        }

        // Here you would normally send an email
        // For now, just return success
        $this->successResponse([], 'Message sent successfully');
    }
}
