<?php

declare(strict_types=1);

namespace App\Http\Controllers;

/**
 * Page Controller
 * Handles public page requests
 */
final class PageController extends Controller
{
    public function index(): void
    {
        // Serve the main index.html file
        $indexPath = BASE_PATH . '/public/index.html';

        if (file_exists($indexPath)) {
            header('Content-Type: text/html; charset=utf-8');
            readfile($indexPath);
        } else {
            http_response_code(404);
            $this->errorResponse('Page not found', 404);
        }
    }

    public function adminLogin(): void
    {
        $this->renderView('admin/login.ejs');
    }

    public function adminOrders(): void
    {
        $this->renderView('admin/orders.ejs');
    }

    public function adminSettings(): void
    {
        $this->renderView('admin/settings.ejs');
    }

    public function debug(): void
    {
        $debugPath = BASE_PATH . '/public/debug.php';

        if (file_exists($debugPath)) {
            header('Content-Type: text/html; charset=utf-8');
            include $debugPath;
        } else {
            echo "Debug file not found!";
        }
    }

    private function renderView(string $view): void
    {
        $viewPath = BASE_PATH . '/resources/views/' . $view;

        if (file_exists($viewPath)) {
            header('Content-Type: text/html; charset=utf-8');
            readfile($viewPath);
        } else {
            http_response_code(404);
            $this->errorResponse('View not found: ' . $view, 404);
        }
    }

    private function errorResponse(string $message, int $code = 500): void
    {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
    }
}
