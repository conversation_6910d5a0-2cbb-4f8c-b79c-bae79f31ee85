<?php

declare(strict_types=1);

/**
 * Laravel-style Entry Point
 * DrxDion E-commerce Platform
 */

// Bootstrap the application
$container = require_once __DIR__ . '/../bootstrap/app.php';

// Simple Router Class
class Router
{
    private array $routes = [];
    private $container;

    public function __construct($container)
    {
        $this->container = $container;
        $this->loadRoutes();
    }

    private function loadRoutes(): void
    {
        // Load web routes
        $webRoutes = require BASE_PATH . '/routes/web.php';
        foreach ($webRoutes as $uri => $route) {
            $this->routes[$uri] = $route;
        }

        // Load API routes
        $apiRoutes = require BASE_PATH . '/routes/api.php';
        foreach ($apiRoutes as $uri => $route) {
            $this->routes[$uri] = $route;
        }
    }

    public function dispatch(): void
    {
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

        // Debug: Log all requests
        error_log("Router: Processing request - URI: $uri, Method: $method");

        // Remove query string
        $uri = strtok($uri, '?');

        // Clean up URI - remove folder name if present
        if (str_starts_with($uri, '/drxdion/')) {
            $uri = substr($uri, 8); // Remove '/drxdion'
            error_log("Router: URI after cleanup: $uri");
        }
        if (empty($uri)) {
            $uri = '/';
        }

        // Normalize URI: remove trailing slash except for root
        if ($uri !== '/' && str_ends_with($uri, '/')) {
            $uri = rtrim($uri, '/');
        }

        // Handle static files first
        if ($this->isStaticFile($uri)) {
            error_log("Router: Serving static file: $uri");
            $this->serveStaticFile($uri);
            return;
        }

        // Debug: Show available routes
        error_log("Router: Available routes: " . implode(', ', array_keys($this->routes)));

        // Try exact match first (normalize route keys too)
        foreach ($this->routes as $routeUri => $route) {
            $normalizedRouteUri = ($routeUri !== '/' && str_ends_with($routeUri, '/')) ? rtrim($routeUri, '/') : $routeUri;
            if ($uri === $normalizedRouteUri && $this->methodMatches($method, $route[0])) {
                $this->callController($route[1]);
                return;
            }
        }

        // Try pattern matching for dynamic routes
        foreach ($this->routes as $pattern => $route) {
            $normalizedPattern = ($pattern !== '/' && str_ends_with($pattern, '/')) ? rtrim($pattern, '/') : $pattern;
            if ($this->patternMatches($uri, $normalizedPattern) && $this->methodMatches($method, $route[0])) {
                $this->callController($route[1]);
                return;
            }
        }

        // Fallback to legacy simple-config for now
        $this->handleLegacyRouting($uri, $method);
    }

    private function methodMatches(string $method, string $allowedMethods): bool
    {
        return in_array($method, explode('|', $allowedMethods));
    }

    private function patternMatches(string $uri, string $pattern): bool
    {
        $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $pattern);
        $result = preg_match('#^' . $pattern . '$#', $uri);
        return $result === 1;
    }

    private function callController(string $controllerAction): void
    {
        [$controller, $method] = explode('@', $controllerAction);

        try {
            // Debug: Log controller call
            error_log("Calling controller: $controller@$method");

            // Use container to resolve controller with dependencies
            $controllerInstance = Container::resolve($controller);
            $controllerInstance->$method();
        } catch (\Exception $e) {
            error_log("Controller error: " . $e->getMessage());
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Controller error: ' . $e->getMessage()
            ]);
        }
    }

    private function handleLegacyRouting(string $uri, string $method): void
    {
        // Include the legacy simple-config router for backward compatibility
        require_once BASE_PATH . '/simple-config.php';
    }

    private function isStaticFile(string $uri): bool
    {
        // Static file extensions
        $extensions = [
            'css',
            'js',
            'jpg',
            'jpeg',
            'png',
            'gif',
            'svg',
            'ico',
            'webp',
            'woff',
            'woff2',
            'ttf',
            'eot',
            'otf',
            'mp4',
            'webm',
            'pdf',
            'zip',
            'txt',
            'xml',
            'json',
            'map'
        ];

        $extension = strtolower(pathinfo($uri, PATHINFO_EXTENSION));
        return in_array($extension, $extensions);
    }

    private function serveStaticFile(string $uri): void
    {
        // Build file path
        $filePath = BASE_PATH . '/public' . $uri;

        // Security check - prevent directory traversal
        $realPath = realpath($filePath);
        $publicPath = realpath(BASE_PATH . '/public');

        if (!$realPath || !str_starts_with($realPath, $publicPath . DIRECTORY_SEPARATOR)) {
            http_response_code(404);
            echo 'File not found';
            return;
        }

        if (!file_exists($realPath)) {
            http_response_code(404);
            echo 'File not found';
            return;
        }

        // Set appropriate content type
        $mimeType = $this->getMimeType($realPath);
        header('Content-Type: ' . $mimeType);

        // Set cache headers for static files
        $maxAge = 86400; // 24 hours
        header('Cache-Control: public, max-age=' . $maxAge);
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT');

        // Output file
        readfile($realPath);
        exit; // Important: exit after serving static file
    }

    private function getMimeType(string $filePath): string
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        $mimeTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'webp' => 'image/webp',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject',
            'otf' => 'font/otf',
            'mp4' => 'video/mp4',
            'webm' => 'video/webm',
            'pdf' => 'application/pdf',
            'zip' => 'application/zip',
            'txt' => 'text/plain',
            'xml' => 'application/xml',
            'html' => 'text/html',
            'htm' => 'text/html',
            'map' => 'application/json'
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}

// Create and dispatch the router
$router = new Router($container);
$router->dispatch();
